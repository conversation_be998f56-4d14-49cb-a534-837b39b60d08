class PortfolioManager {
    constructor() {
        this.totalCapital = 980250.00;
        this.allocations = new Map();
        this.init();
    }

    init() {
        this.setupSliders();
        this.setupDragAndDrop();
        this.setupEditFunctionality();
        this.updateDisplay();
    }

    setupSliders() {
        const sliders = document.querySelectorAll('.allocation-slider');
        
        sliders.forEach(slider => {
            const assetName = slider.dataset.asset;
            const initialValue = parseFloat(slider.value);
            this.allocations.set(assetName, initialValue);

            slider.addEventListener('input', (e) => {
                this.handleSliderChange(e.target);
            });

            slider.addEventListener('mouseenter', () => {
                this.setCardActive(slider.closest('.asset-card'), true);
            });

            slider.addEventListener('mouseleave', () => {
                this.setCardActive(slider.closest('.asset-card'), false);
            });
        });
    }

    handleSliderChange(slider) {
        const assetName = slider.dataset.asset;
        const percentage = parseFloat(slider.value);
        const card = slider.closest('.asset-card');
        
        // Update allocation
        this.allocations.set(assetName, percentage);
        
        // Update percentage display
        const percentageDisplay = card.querySelector('.percentage-display');
        percentageDisplay.textContent = `${percentage}%`;
        
        // Update allocation value
        const allocationValue = card.querySelector('.allocation-value');
        const value = (this.totalCapital * percentage) / 100;
        allocationValue.textContent = this.formatCurrency(value);
        
        // Update remaining amount
        this.updateRemainingAmount();
        
        // Set card as active during interaction
        this.setCardActive(card, true);
    }

    setCardActive(card, isActive) {
        if (isActive) {
            card.classList.add('active');
        } else {
            // Only remove active class if slider value is 0
            const slider = card.querySelector('.allocation-slider');
            if (parseFloat(slider.value) === 0) {
                card.classList.remove('active');
            }
        }
    }

    updateRemainingAmount() {
        const totalAllocated = Array.from(this.allocations.values())
            .reduce((sum, percentage) => sum + percentage, 0);
        
        const remainingPercentage = Math.max(0, 100 - totalAllocated);
        const remainingAmount = (this.totalCapital * remainingPercentage) / 100;
        
        const remainingValueElement = document.querySelector('.remaining-value');
        remainingValueElement.textContent = this.formatCurrency(remainingAmount);
        
        // Update color based on remaining amount
        if (remainingAmount < 0) {
            remainingValueElement.style.color = '#ef4444';
        } else if (remainingAmount === 0) {
            remainingValueElement.style.color = '#10b981';
        } else {
            remainingValueElement.style.color = 'white';
        }
    }

    setupDragAndDrop() {
        const assetChips = document.querySelectorAll('.asset-chip');
        
        assetChips.forEach(chip => {
            chip.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData('text/plain', '');
                chip.style.opacity = '0.5';
            });

            chip.addEventListener('dragend', () => {
                chip.style.opacity = '1';
            });

            chip.addEventListener('dragover', (e) => {
                e.preventDefault();
            });

            chip.addEventListener('drop', (e) => {
                e.preventDefault();
                const draggedChip = document.querySelector('.asset-chip[style*="opacity: 0.5"]');
                if (draggedChip && draggedChip !== chip) {
                    const container = chip.parentNode;
                    const draggedIndex = Array.from(container.children).indexOf(draggedChip);
                    const targetIndex = Array.from(container.children).indexOf(chip);
                    
                    if (draggedIndex < targetIndex) {
                        container.insertBefore(draggedChip, chip.nextSibling);
                    } else {
                        container.insertBefore(draggedChip, chip);
                    }
                }
            });
        });
    }

    setupEditFunctionality() {
        const editIcons = document.querySelectorAll('.edit-icon');
        
        editIcons.forEach(icon => {
            icon.addEventListener('click', (e) => {
                e.stopPropagation();
                this.editAllocationValue(icon.closest('.asset-card'));
            });
        });
    }

    editAllocationValue(card) {
        const allocationValueElement = card.querySelector('.allocation-value');
        const currentValue = allocationValueElement.textContent;
        
        // Create input element
        const input = document.createElement('input');
        input.type = 'text';
        input.value = currentValue;
        input.className = 'edit-input';
        input.style.cssText = `
            width: 100%;
            padding: 8px 12px;
            border: 2px solid #f59e0b;
            border-radius: 8px;
            font-size: 18px;
            font-weight: 700;
            text-align: center;
            background: white;
        `;
        
        // Replace allocation value with input
        allocationValueElement.style.display = 'none';
        allocationValueElement.parentNode.insertBefore(input, allocationValueElement.nextSibling);
        input.focus();
        input.select();
        
        const finishEdit = () => {
            const newValue = this.parseCurrency(input.value);
            if (!isNaN(newValue) && newValue >= 0) {
                const percentage = (newValue / this.totalCapital) * 100;
                const slider = card.querySelector('.allocation-slider');
                const assetName = slider.dataset.asset;
                
                // Update slider and allocation
                slider.value = percentage;
                this.allocations.set(assetName, percentage);
                
                // Update displays
                allocationValueElement.textContent = this.formatCurrency(newValue);
                card.querySelector('.percentage-display').textContent = `${percentage.toFixed(1)}%`;
                
                this.updateRemainingAmount();
                this.setCardActive(card, percentage > 0);
            }
            
            // Remove input and show original element
            input.remove();
            allocationValueElement.style.display = 'block';
        };
        
        input.addEventListener('blur', finishEdit);
        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                finishEdit();
            }
        });
    }

    updateDisplay() {
        // Set initial active states
        document.querySelectorAll('.asset-card').forEach(card => {
            const slider = card.querySelector('.allocation-slider');
            const percentage = parseFloat(slider.value);
            if (percentage > 0) {
                card.classList.add('active');
            }
        });
        
        this.updateRemainingAmount();
    }

    formatCurrency(value) {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
            minimumFractionDigits: 2
        }).format(value);
    }

    parseCurrency(value) {
        // Remove currency symbols and convert to number
        return parseFloat(value.replace(/[^\d,.-]/g, '').replace(',', '.'));
    }
}

// Initialize the portfolio manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new PortfolioManager();
});

// Add smooth scrolling for sidebar
document.querySelector('.assets-section').addEventListener('wheel', (e) => {
    e.currentTarget.scrollTop += e.deltaY;
});
