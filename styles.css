* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    display: flex;
    min-height: 100vh;
    gap: 24px;
    padding: 24px;
    max-width: 1400px;
    margin: 0 auto;
}

/* Main Grid Layout */
.main-grid {
    flex: 1;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 16px;
    min-height: 600px;
}

/* Asset Cards */
.asset-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.asset-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.asset-card.active {
    border: 2px solid #f59e0b;
    box-shadow: 0 4px 16px rgba(245, 158, 11, 0.2);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.category-label {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.edit-icon {
    color: #9ca3af;
    font-size: 14px;
    opacity: 0;
    transition: opacity 0.3s ease;
    cursor: pointer;
}

.asset-card:hover .edit-icon {
    opacity: 1;
}

.edit-icon:hover {
    color: #f59e0b;
}

.asset-type {
    font-size: 16px;
    font-weight: 600;
    color: #111827;
    margin-bottom: 16px;
}

.allocation-value {
    font-size: 18px;
    font-weight: 700;
    color: #111827;
    background: #f9fafb;
    padding: 8px 12px;
    border-radius: 8px;
    margin-bottom: 20px;
    text-align: center;
}

.slider-container {
    position: relative;
}

.allocation-slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #e5e7eb;
    outline: none;
    -webkit-appearance: none;
    cursor: pointer;
}

.allocation-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #f59e0b;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.allocation-slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(245, 158, 11, 0.4);
}

.allocation-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #f59e0b;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.percentage-display {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: #111827;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.asset-card.active .percentage-display,
.asset-card:hover .percentage-display {
    opacity: 1;
}

/* Right Sidebar */
.sidebar {
    width: 320px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    height: fit-content;
}

.portfolio-header {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    padding: 24px;
    text-align: center;
}

.total-label {
    font-size: 14px;
    opacity: 0.9;
    margin-bottom: 4px;
}

.total-value {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 16px;
}

.remaining-label {
    font-size: 12px;
    opacity: 0.8;
    margin-bottom: 4px;
}

.remaining-value {
    font-size: 16px;
    font-weight: 600;
}

.assets-section {
    padding: 24px;
    max-height: 500px;
    overflow-y: auto;
}

.assets-section h3 {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin-bottom: 20px;
}

.asset-category {
    margin-bottom: 24px;
}

.asset-category h4 {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 12px;
}

.asset-chips {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.asset-chip {
    display: flex;
    align-items: center;
    gap: 8px;
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 14px;
    color: #374151;
    cursor: move;
    transition: all 0.3s ease;
}

.asset-chip:hover {
    background: #f3f4f6;
    border-color: #d1d5db;
}

.drag-handle {
    color: #9ca3af;
    font-size: 12px;
    cursor: grab;
}

.drag-handle:active {
    cursor: grabbing;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-grid {
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(4, 1fr);
    }
}

@media (max-width: 768px) {
    .container {
        flex-direction: column;
        padding: 16px;
    }
    
    .main-grid {
        grid-template-columns: 1fr;
        grid-template-rows: repeat(6, 1fr);
        min-height: auto;
    }
    
    .sidebar {
        width: 100%;
        order: -1;
    }
    
    .assets-section {
        max-height: 300px;
    }
}
