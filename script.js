class PortfolioManager {
  constructor() {
    this.totalCapital = 980250.0;
    this.allocations = new Map();
    this.snapPoints = [
      0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90,
      95, 100,
    ];
    this.odometers = new Map(); // Track odometer instances
    this.init();
  }

  init() {
    this.reorganizeExistingCards(); // Reorganize existing HTML cards
    this.setupSliders();
    this.setupDragAndDrop();
    this.setupDoubleClickEdit(); // Replace setupEditFunctionality with double-click
    this.setupSortableJS();
    this.setupOdometers(); // Initialize odometers
    this.updateDisplay();
  }

  setupSliders() {
    const sliders = document.querySelectorAll(".allocation-slider");

    sliders.forEach((slider) => {
      const assetName = slider.dataset.asset;
      const initialValue = this.snapToNearestPoint(parseFloat(slider.value));
      slider.value = initialValue;
      this.allocations.set(assetName, initialValue);

      // Prevent card drag when interacting with slider
      slider.addEventListener("mousedown", (e) => {
        e.stopPropagation();
        this.showTooltip(slider);
      });

      slider.addEventListener("touchstart", (e) => {
        e.stopPropagation();
        this.showTooltip(slider);
      });

      // Handle slider input with snapping
      slider.addEventListener("input", (e) => {
        this.handleSliderInput(e.target);
      });

      // Handle slider change (when user releases)
      slider.addEventListener("change", (e) => {
        this.handleSliderChange(e.target);
      });

      // Show tooltip on mouse enter and during interaction
      slider.addEventListener("mouseenter", () => {
        this.showTooltip(slider);
        this.setCardActive(slider.closest(".asset-card"), true);
      });

      slider.addEventListener("mouseleave", () => {
        this.hideTooltip(slider);
        this.setCardActive(slider.closest(".asset-card"), false);
      });

      slider.addEventListener("mouseup", () => {
        // Keep tooltip visible briefly after release
        setTimeout(() => {
          if (!slider.matches(":hover")) {
            this.hideTooltip(slider);
          }
        }, 500);
      });

      // Prevent drag on slider container
      const sliderContainer = slider.closest(".slider-container");
      if (sliderContainer) {
        sliderContainer.addEventListener("mousedown", (e) => {
          e.stopPropagation();
        });

        sliderContainer.addEventListener("touchstart", (e) => {
          e.stopPropagation();
        });
      }

      // Position tooltip initially
      this.positionTooltip(slider);
    });
  }

  snapToNearestPoint(value) {
    let closest = this.snapPoints[0];
    let minDiff = Math.abs(value - closest);

    for (let point of this.snapPoints) {
      const diff = Math.abs(value - point);
      if (diff < minDiff) {
        minDiff = diff;
        closest = point;
      }
    }

    return closest;
  }

  handleSliderInput(slider) {
    const currentValue = parseFloat(slider.value);
    const assetName = slider.dataset.asset;

    // Real-time positioning of tooltip during drag
    this.positionTooltip(slider);

    // Show current value in tooltip (before snapping)
    const card = slider.closest(".asset-card");
    const percentageDisplay = card.querySelector(".percentage-display");
    percentageDisplay.textContent = `${currentValue.toFixed(1)}%`;

    // Update progress fill in real-time
    this.updateSliderProgress(slider, currentValue);

    // Real-time allocation updates
    this.allocations.set(assetName, currentValue);

    // Update allocation value in real-time with odometer
    const allocationValue = card.querySelector(".allocation-value");
    const value = (this.totalCapital * currentValue) / 100;
    this.updateOdometerValue(allocationValue, value);

    // Update remaining amount in real-time
    this.updateRemainingAmount();

    // Update card active state in real-time
    this.setCardActive(card, currentValue > 0);
  }

  handleSliderChange(slider) {
    const assetName = slider.dataset.asset;
    const rawPercentage = parseFloat(slider.value);

    // Snap to nearest point
    const snappedPercentage = this.snapToNearestPoint(rawPercentage);
    slider.value = snappedPercentage;

    const card = slider.closest(".asset-card");

    // Update allocation
    this.allocations.set(assetName, snappedPercentage);

    // Update percentage display with snapped value
    const percentageDisplay = card.querySelector(".percentage-display");
    percentageDisplay.textContent = `${snappedPercentage}%`;

    // Update allocation value with odometer
    const allocationValue = card.querySelector(".allocation-value");
    const value = (this.totalCapital * snappedPercentage) / 100;
    this.updateOdometerValue(allocationValue, value);

    // Update remaining amount
    this.updateRemainingAmount();

    // Set card as active during interaction
    this.setCardActive(card, snappedPercentage > 0);

    // Reposition tooltip after snapping
    this.positionTooltip(slider);

    // Add haptic feedback effect
    this.addSnapFeedback(slider);
  }

  positionTooltip(slider) {
    const card = slider.closest(".asset-card");
    const percentageDisplay = card.querySelector(".percentage-display");

    // Calculate thumb position based on slider value
    const percentage = parseFloat(slider.value);
    const thumbPosition = (percentage / 100) * slider.offsetWidth;

    // Position tooltip directly above the thumb
    percentageDisplay.style.left = `${thumbPosition}px`;
    percentageDisplay.style.transform = "translateX(-50%)";

    // Update slider progress fill
    this.updateSliderProgress(slider, percentage);
  }

  updateSliderProgress(slider, percentage) {
    // Update CSS custom property for progress fill
    slider.style.setProperty("--progress", `${percentage}%`);
  }

  setupOdometers() {
    // Check if Odometer is available
    if (typeof Odometer === "undefined") {
      console.warn("Odometer library not loaded, using fallback text updates");
      return;
    }

    // Setup odometers for all allocation values
    document.querySelectorAll(".allocation-value").forEach((element, index) => {
      if (!element.classList.contains("odometer")) {
        element.classList.add("odometer");
        const odometer = new Odometer({
          el: element,
          value: 0,
          format: "", // We'll handle formatting ourselves
          theme: "default",
          duration: 500,
        });
        this.odometers.set(element, odometer);
      }
    });

    // Setup odometer for remaining value
    const remainingValueElement = document.querySelector(".remaining-value");
    if (
      remainingValueElement &&
      !remainingValueElement.classList.contains("odometer")
    ) {
      remainingValueElement.classList.add("odometer");
      const remainingOdometer = new Odometer({
        el: remainingValueElement,
        value: 0,
        format: "",
        theme: "default",
        duration: 500,
      });
      this.odometers.set(remainingValueElement, remainingOdometer);
    }

    // Setup odometer for total value
    const totalValueElement = document.querySelector(".total-value");
    if (
      totalValueElement &&
      !totalValueElement.classList.contains("odometer")
    ) {
      totalValueElement.classList.add("odometer");
      const totalOdometer = new Odometer({
        el: totalValueElement,
        value: 0,
        format: "",
        theme: "default",
        duration: 500,
      });
      this.odometers.set(totalValueElement, totalOdometer);
    }
  }

  updateOdometerValue(element, newValue) {
    const odometer = this.odometers.get(element);
    if (odometer) {
      // Convert currency value to number for odometer
      const numericValue = Math.round(newValue);
      odometer.update(numericValue);

      // Format the display after animation
      setTimeout(() => {
        element.textContent = this.formatCurrency(newValue);
      }, 550); // Slightly after animation completes
    } else {
      element.textContent = this.formatCurrency(newValue);
    }
  }

  reorganizeExistingCards() {
    // Reorganize all existing cards to use the new header structure
    const cards = document.querySelectorAll(".asset-card");

    cards.forEach((card) => {
      const header = card.querySelector(".card-header");
      const categoryLabel = header.querySelector(".category-label");
      const assetTypeElement = card.querySelector(".asset-type");
      const editIcon = header.querySelector(".edit-icon");
      const allocationValue = card.querySelector(".allocation-value");

      // Add odometer class to allocation value
      if (allocationValue && !allocationValue.classList.contains("odometer")) {
        allocationValue.classList.add("odometer");
      }

      if (categoryLabel && assetTypeElement) {
        const categoryText = categoryLabel.textContent.trim();
        const assetTypeText = assetTypeElement.textContent.trim();

        // Create new header structure
        const headerContent = document.createElement("div");
        headerContent.className = "header-content";

        const categoryAssetLine = document.createElement("div");
        categoryAssetLine.className = "category-asset-line";
        categoryAssetLine.innerHTML = `${categoryText}<span class="separator">></span><span class="asset-type-text">${assetTypeText}</span>`;

        headerContent.appendChild(categoryAssetLine);

        // Clear existing header content and add new structure
        header.innerHTML = "";
        header.appendChild(headerContent);
        header.appendChild(editIcon);

        // Hide the old asset-type element
        assetTypeElement.style.display = "none";
      }
    });
  }

  showTooltip(slider) {
    const card = slider.closest(".asset-card");
    const percentageDisplay = card.querySelector(".percentage-display");
    percentageDisplay.style.opacity = "1";
    this.positionTooltip(slider);
  }

  hideTooltip(slider) {
    const card = slider.closest(".asset-card");
    const percentageDisplay = card.querySelector(".percentage-display");

    // Only hide if card is not active and not being hovered
    const currentValue = parseFloat(slider.value);
    if (currentValue === 0 && !card.matches(":hover")) {
      percentageDisplay.style.opacity = "0";
    }
  }

  addSnapFeedback(slider) {
    // Visual feedback for snapping
    slider.style.transform = "scale(1.02)";
    setTimeout(() => {
      slider.style.transform = "scale(1)";
    }, 150);
  }

  setupSortableJS() {
    // Setup main grid sortable
    this.setupMainGridSortable();

    // Setup sidebar chips sortable
    this.setupSidebarChipsSortable();
  }

  setupMainGridSortable() {
    const mainGrid = document.getElementById("main-grid");

    this.mainGridSortable = Sortable.create(mainGrid, {
      group: {
        name: "portfolio",
        pull: true,
        put: (to, from, dragEl) => {
          // Check if grid has space (max 9 items)
          return this.getGridItemCount() < 9;
        },
      },
      animation: 300,
      ghostClass: "sortable-ghost",
      chosenClass: "sortable-chosen",
      dragClass: "sortable-drag",
      handle: ".card-header, .asset-type, .allocation-value", // Prevent dragging when interacting with sliders

      onStart: (evt) => {
        mainGrid.classList.add("drag-over");
        // Disable slider interactions during drag
        this.setSliderInteractionState(false);
      },

      onEnd: (evt) => {
        mainGrid.classList.remove("drag-over");
        // Re-enable slider interactions after drag
        this.setSliderInteractionState(true);
        this.handleMainGridDrop();
      },

      onAdd: (evt) => {
        // Handle chip to card transformation
        if (evt.item.classList.contains("asset-chip")) {
          this.transformChipToCard(evt.item, evt.newIndex);
        }
      },

      onRemove: (evt) => {
        // Handle card to chip transformation
        if (evt.item.classList.contains("asset-card")) {
          this.transformCardToChip(evt.item, evt.from);
        }
      },
    });
  }

  setupSidebarChipsSortable() {
    // Setup sortable for each chip container
    const chipContainers = document.querySelectorAll(".asset-chips");

    chipContainers.forEach((container) => {
      Sortable.create(container, {
        group: {
          name: "portfolio",
          pull: "clone",
          put: true,
        },
        animation: 300,
        ghostClass: "sortable-ghost",
        chosenClass: "sortable-chosen",
        dragClass: "sortable-drag",

        onStart: (evt) => {
          container.classList.add("drag-over");
        },

        onEnd: (evt) => {
          container.classList.remove("drag-over");
        },

        onAdd: (evt) => {
          // Handle card to chip transformation when dropped on sidebar
          if (evt.item.classList.contains("asset-card")) {
            this.transformCardToChip(evt.item, evt.to);
          }
        },
      });
    });
  }

  transformChipToCard(chipElement, gridIndex) {
    const assetId = chipElement.dataset.assetId;
    const category = chipElement.dataset.category;
    const assetType = chipElement.dataset.assetType;

    // Create new card element
    const cardHTML = this.createCardHTML(assetId, category, assetType);

    // Replace chip with card
    chipElement.outerHTML = cardHTML;

    // Get the newly created card
    const newCard = document.querySelector(`[data-asset-id="${assetId}"]`);

    // Add transformation animation
    newCard.classList.add("transforming-to-card");
    setTimeout(() => {
      newCard.classList.remove("transforming-to-card");
    }, 300);

    // Setup slider functionality for the new card
    this.setupCardSlider(newCard);

    // Update portfolio calculations
    this.updateRemainingAmount();
  }

  transformCardToChip(cardElement, targetContainer) {
    const assetId = cardElement.dataset.assetId;
    const category = cardElement.dataset.category;

    // Get the actual asset type text from the card element
    const assetTypeElement = cardElement.querySelector(".asset-type");
    const assetType = assetTypeElement
      ? assetTypeElement.textContent.trim()
      : cardElement.dataset.assetType;

    // Reset allocation before converting to chip
    const assetName = cardElement.dataset.asset;
    if (this.allocations.has(assetName)) {
      this.allocations.set(assetName, 0);
    }

    // Create new chip element
    const chipHTML = this.createChipHTML(assetId, category, assetType);

    // Add transformation animation
    cardElement.classList.add("transforming-to-chip");

    setTimeout(() => {
      // Replace card with chip
      cardElement.outerHTML = chipHTML;

      // Update portfolio calculations
      this.updateRemainingAmount();
    }, 300);
  }

  createCardHTML(assetId, category, assetType) {
    const categoryLabel = this.getCategoryLabel(category);
    const assetName = assetId.replace("-available", "").replace("-", "_");

    return `
      <div class="asset-card" data-category="${category}" data-asset="${assetName}" data-asset-id="${assetId}" data-asset-type="${assetType}">
        <div class="card-header">
          <div class="header-content">
            <div class="category-asset-line">${categoryLabel}<span class="separator">></span><span class="asset-type-text">${assetType}</span></div>
          </div>
          <i class="fas fa-edit edit-icon"></i>
        </div>
        <div class="asset-type" style="display: none;">${assetType}</div>
        <div class="allocation-value odometer">R$0</div>
        <div class="slider-container">
          <input type="range" class="allocation-slider" min="0" max="100" value="0" data-asset="${assetName}">
          <div class="percentage-display">0%</div>
        </div>
      </div>
    `;
  }

  createChipHTML(assetId, category, assetType) {
    return `
      <div class="asset-chip" draggable="true" data-asset-id="${assetId}" data-category="${category}" data-asset-type="${assetType}">
        <i class="fas fa-grip-vertical drag-handle"></i>
        <span>${assetType}</span>
      </div>
    `;
  }

  getCategoryLabel(category) {
    const labels = {
      "renda-fixa": "Renda fixa",
      fundos: "Fund. de Investimento",
      outros: "Outros",
    };
    return labels[category] || category;
  }

  setupCardSlider(card) {
    const slider = card.querySelector(".allocation-slider");
    const assetName = slider.dataset.asset;

    // Initialize allocation
    this.allocations.set(assetName, 0);

    // Prevent card drag when interacting with slider
    slider.addEventListener("mousedown", (e) => {
      e.stopPropagation();
      this.showTooltip(slider);
    });

    slider.addEventListener("touchstart", (e) => {
      e.stopPropagation();
      this.showTooltip(slider);
    });

    // Setup event listeners
    slider.addEventListener("input", (e) => {
      this.handleSliderInput(e.target);
    });

    slider.addEventListener("change", (e) => {
      this.handleSliderChange(e.target);
    });

    slider.addEventListener("mouseenter", () => {
      this.showTooltip(slider);
      this.setCardActive(card, true);
    });

    slider.addEventListener("mouseleave", () => {
      this.hideTooltip(slider);
      this.setCardActive(card, false);
    });

    slider.addEventListener("mouseup", () => {
      setTimeout(() => {
        if (!slider.matches(":hover")) {
          this.hideTooltip(slider);
        }
      }, 500);
    });

    // Prevent drag on slider container
    const sliderContainer = card.querySelector(".slider-container");
    if (sliderContainer) {
      sliderContainer.addEventListener("mousedown", (e) => {
        e.stopPropagation();
      });

      sliderContainer.addEventListener("touchstart", (e) => {
        e.stopPropagation();
      });
    }

    // Setup edit functionality with double-click
    card.addEventListener("dblclick", (e) => {
      e.preventDefault();
      e.stopPropagation();
      this.editAllocationValue(card);
    });

    // Setup odometer for this card's allocation value
    const allocationValue = card.querySelector(".allocation-value");
    if (allocationValue && !allocationValue.classList.contains("odometer")) {
      allocationValue.classList.add("odometer");
      if (typeof Odometer !== "undefined") {
        const odometer = new Odometer({
          el: allocationValue,
          value: 0,
          format: "",
          theme: "default",
          duration: 500,
        });
        this.odometers.set(allocationValue, odometer);
      }
    }

    // Position tooltip and set progress
    this.positionTooltip(slider);
    this.updateSliderProgress(slider, 0);
  }

  getGridItemCount() {
    const mainGrid = document.getElementById("main-grid");
    return mainGrid.children.length;
  }

  handleMainGridDrop() {
    // Handle reordering within grid or other grid-specific logic
    this.updateRemainingAmount();
  }

  setCardActive(card, isActive) {
    const slider = card.querySelector(".allocation-slider");
    const currentValue = parseFloat(slider.value);

    if (isActive || currentValue > 0) {
      card.classList.add("active");
    } else {
      card.classList.remove("active");
    }
  }

  setSliderInteractionState(enabled) {
    const sliders = document.querySelectorAll(".allocation-slider");
    sliders.forEach((slider) => {
      slider.style.pointerEvents = enabled ? "auto" : "none";
      const container = slider.closest(".slider-container");
      if (container) {
        container.style.pointerEvents = enabled ? "auto" : "none";
      }
    });
  }

  updateRemainingAmount() {
    const totalAllocated = Array.from(this.allocations.values()).reduce(
      (sum, percentage) => sum + percentage,
      0
    );

    const remainingPercentage = Math.max(0, 100 - totalAllocated);
    const remainingAmount = (this.totalCapital * remainingPercentage) / 100;

    const remainingValueElement = document.querySelector(".remaining-value");

    // Use odometer for animated update
    this.updateOdometerValue(remainingValueElement, remainingAmount);

    // Update color based on remaining amount
    if (remainingAmount < 0) {
      remainingValueElement.style.color = "#ef4444";
    } else if (remainingAmount === 0) {
      remainingValueElement.style.color = "#10b981";
    } else {
      remainingValueElement.style.color = "white";
    }
  }

  setupDragAndDrop() {
    const assetChips = document.querySelectorAll(".asset-chip");

    assetChips.forEach((chip) => {
      chip.addEventListener("dragstart", (e) => {
        e.dataTransfer.setData("text/plain", "");
        chip.style.opacity = "0.5";
      });

      chip.addEventListener("dragend", () => {
        chip.style.opacity = "1";
      });

      chip.addEventListener("dragover", (e) => {
        e.preventDefault();
      });

      chip.addEventListener("drop", (e) => {
        e.preventDefault();
        const draggedChip = document.querySelector(
          '.asset-chip[style*="opacity: 0.5"]'
        );
        if (draggedChip && draggedChip !== chip) {
          const container = chip.parentNode;
          const draggedIndex = Array.from(container.children).indexOf(
            draggedChip
          );
          const targetIndex = Array.from(container.children).indexOf(chip);

          if (draggedIndex < targetIndex) {
            container.insertBefore(draggedChip, chip.nextSibling);
          } else {
            container.insertBefore(draggedChip, chip);
          }
        }
      });
    });
  }

  setupDoubleClickEdit() {
    // Remove old edit icon functionality and add double-click to cards
    document.querySelectorAll(".asset-card").forEach((card) => {
      card.addEventListener("dblclick", (e) => {
        e.preventDefault();
        e.stopPropagation();
        this.editAllocationValue(card);
      });
    });
  }

  setupEditFunctionality() {
    // Legacy method - now handled by setupDoubleClickEdit
    this.setupDoubleClickEdit();
  }

  editAllocationValue(card) {
    const allocationValueElement = card.querySelector(".allocation-value");
    const currentValue = allocationValueElement.textContent;

    // Extract numeric value from formatted currency
    const numericValue = this.parseCurrency(currentValue);

    // Create input element
    const input = document.createElement("input");
    input.type = "text";
    input.value = numericValue.toFixed(2);
    input.className = "edit-input";
    input.style.cssText = `
      width: 100%;
      padding: 8px 12px;
      border: 2px solid #C49725;
      border-radius: 8px;
      font-size: 20px;
      font-weight: 600;
      text-align: center;
      background: white;
      color: #1e293b;
    `;

    // Replace allocation value with input
    allocationValueElement.style.display = "none";
    allocationValueElement.parentNode.insertBefore(
      input,
      allocationValueElement.nextSibling
    );

    let autoNumeric = null;

    // Initialize AutoNumeric if available
    if (typeof AutoNumeric !== "undefined") {
      try {
        autoNumeric = new AutoNumeric(input, {
          currencySymbol: "R$ ",
          currencySymbolPlacement:
            AutoNumeric.options.currencySymbolPlacement.prefix,
          decimalCharacter: ",",
          digitGroupSeparator: ".",
          decimalPlaces: 2,
          minimumValue: 0,
          maximumValue: this.totalCapital,
          wheelStep: 1000,
          selectOnFocus: true,
        });
      } catch (e) {
        console.warn("AutoNumeric initialization failed, using basic input");
      }
    }

    input.focus();
    input.select();

    const finishEdit = () => {
      let newValue;

      if (autoNumeric) {
        newValue = autoNumeric.getNumber();
      } else {
        newValue = this.parseCurrency(input.value);
      }

      if (!isNaN(newValue) && newValue >= 0) {
        const percentage = (newValue / this.totalCapital) * 100;
        const slider = card.querySelector(".allocation-slider");
        const assetName = slider.dataset.asset;

        // Update slider and allocation
        slider.value = percentage;
        this.allocations.set(assetName, percentage);

        // Update displays with odometer animation
        this.updateOdometerValue(allocationValueElement, newValue);
        card.querySelector(
          ".percentage-display"
        ).textContent = `${percentage.toFixed(1)}%`;

        this.updateRemainingAmount();
        this.setCardActive(card, percentage > 0);
        this.positionTooltip(slider);
        this.updateSliderProgress(slider, percentage);
      }

      // Clean up AutoNumeric and remove input
      if (autoNumeric) {
        autoNumeric.remove();
      }
      input.remove();
      allocationValueElement.style.display = "block";
    };

    input.addEventListener("blur", finishEdit);
    input.addEventListener("keypress", (e) => {
      if (e.key === "Enter") {
        finishEdit();
      }
    });
  }

  updateDisplay() {
    // Set initial active states and position tooltips
    document.querySelectorAll(".asset-card").forEach((card) => {
      const slider = card.querySelector(".allocation-slider");
      const percentage = parseFloat(slider.value);

      // Snap initial values
      const snappedValue = this.snapToNearestPoint(percentage);
      slider.value = snappedValue;
      this.allocations.set(slider.dataset.asset, snappedValue);

      // Update displays
      const percentageDisplay = card.querySelector(".percentage-display");
      percentageDisplay.textContent = `${snappedValue}%`;

      const allocationValue = card.querySelector(".allocation-value");
      const value = (this.totalCapital * snappedValue) / 100;

      // Initialize with immediate value (no animation on first load)
      allocationValue.textContent = this.formatCurrency(value);

      // Set active state
      if (snappedValue > 0) {
        card.classList.add("active");
      }

      // Position tooltip and set progress
      this.positionTooltip(slider);
      this.updateSliderProgress(slider, snappedValue);
    });

    // Initialize total value odometer
    const totalValueElement = document.querySelector(".total-value");
    if (totalValueElement) {
      totalValueElement.textContent = this.formatCurrency(this.totalCapital);
    }

    this.updateRemainingAmount();
  }

  formatCurrency(value) {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
      minimumFractionDigits: 2,
    }).format(value);
  }

  parseCurrency(value) {
    // Remove currency symbols and convert to number
    return parseFloat(value.replace(/[^\d,.-]/g, "").replace(",", "."));
  }
}

// Initialize the portfolio manager when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  // Wait a bit for external libraries to load
  setTimeout(() => {
    new PortfolioManager();
  }, 100);
});

// Add smooth scrolling for sidebar
document.querySelector(".assets-section").addEventListener("wheel", (e) => {
  e.currentTarget.scrollTop += e.deltaY;
});
